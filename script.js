class TwistEggMachine {
    constructor() {
        this.canvas = document.getElementById('ballCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.drawButton = document.getElementById('drawButton');
        this.resultBall = document.getElementById('resultBall');
        this.resultText = document.getElementById('resultText');
        
        this.balls = [];
        this.isDrawing = false;
        this.animationId = null;
        
        this.init();
    }
    
    init() {
        this.createBalls();
        this.drawBalls();
        this.drawButton.addEventListener('click', () => this.startDraw());
    }
    
    createBalls() {
        // 创建1-100的数字球
        for (let i = 1; i <= 100; i++) {
            this.balls.push({
                number: i,
                x: Math.random() * (this.canvas.width - 30) + 15,
                y: Math.random() * (this.canvas.height - 30) + 15,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                radius: 12,
                color: this.getRandomColor(),
                selected: false
            });
        }
    }
    
    getRandomColor() {
        const colors = [
            '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', 
            '#ffeaa7', '#dda0dd', '#98d8c8', '#f7dc6f',
            '#bb8fce', '#85c1e9', '#f8c471', '#82e0aa'
        ];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    drawBalls() {
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        this.balls.forEach(ball => {
            if (!ball.selected) {
                this.ctx.save();
                
                // 绘制球体阴影
                this.ctx.shadowColor = 'rgba(0, 0, 0, 0.3)';
                this.ctx.shadowBlur = 5;
                this.ctx.shadowOffsetX = 2;
                this.ctx.shadowOffsetY = 2;
                
                // 绘制球体
                this.ctx.beginPath();
                this.ctx.arc(ball.x, ball.y, ball.radius, 0, Math.PI * 2);
                this.ctx.fillStyle = ball.color;
                this.ctx.fill();
                
                // 绘制高光效果
                this.ctx.shadowColor = 'transparent';
                this.ctx.beginPath();
                this.ctx.arc(ball.x - 3, ball.y - 3, ball.radius * 0.3, 0, Math.PI * 2);
                this.ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
                this.ctx.fill();
                
                // 绘制数字
                this.ctx.fillStyle = 'white';
                this.ctx.font = 'bold 10px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.textBaseline = 'middle';
                this.ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
                this.ctx.lineWidth = 1;
                this.ctx.strokeText(ball.number.toString(), ball.x, ball.y);
                this.ctx.fillText(ball.number.toString(), ball.x, ball.y);
                
                this.ctx.restore();
            }
        });
    }
    
    updateBalls() {
        this.balls.forEach(ball => {
            if (!ball.selected) {
                // 更新位置
                ball.x += ball.vx;
                ball.y += ball.vy;
                
                // 边界碰撞检测
                if (ball.x <= ball.radius || ball.x >= this.canvas.width - ball.radius) {
                    ball.vx *= -0.8;
                    ball.x = Math.max(ball.radius, Math.min(this.canvas.width - ball.radius, ball.x));
                }
                if (ball.y <= ball.radius || ball.y >= this.canvas.height - ball.radius) {
                    ball.vy *= -0.8;
                    ball.y = Math.max(ball.radius, Math.min(this.canvas.height - ball.radius, ball.y));
                }
                
                // 添加重力效果
                ball.vy += 0.1;
                
                // 摩擦力
                ball.vx *= 0.99;
                ball.vy *= 0.99;
            }
        });
    }
    
    startDraw() {
        if (this.isDrawing) return;
        
        this.isDrawing = true;
        this.drawButton.disabled = true;
        this.drawButton.textContent = '抽奖中...';
        
        // 隐藏之前的结果
        this.resultBall.classList.add('hidden');
        this.resultText.textContent = '';
        
        // 开始摇晃动画
        this.canvas.parentElement.classList.add('shaking');
        
        // 让球开始剧烈运动
        this.balls.forEach(ball => {
            if (!ball.selected) {
                ball.vx = (Math.random() - 0.5) * 8;
                ball.vy = (Math.random() - 0.5) * 8;
            }
        });
        
        // 运行摇晃动画
        this.animate();
        
        // 3秒后选择一个球
        setTimeout(() => {
            this.selectWinningBall();
        }, 3000);
    }
    
    animate() {
        this.updateBalls();
        this.drawBalls();
        
        if (this.isDrawing) {
            this.animationId = requestAnimationFrame(() => this.animate());
        }
    }
    
    selectWinningBall() {
        // 停止摇晃
        this.canvas.parentElement.classList.remove('shaking');
        
        // 随机选择一个球
        const availableBalls = this.balls.filter(ball => !ball.selected);
        const winningBall = availableBalls[Math.floor(Math.random() * availableBalls.length)];
        winningBall.selected = true;
        
        // 停止动画
        this.isDrawing = false;
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        // 显示结果
        this.showResult(winningBall);
        
        // 重绘画布（移除选中的球）
        this.drawBalls();
        
        // 重置按钮
        this.drawButton.disabled = false;
        this.drawButton.textContent = '抽奖';
    }
    
    showResult(ball) {
        // 设置结果球的样式
        this.resultBall.style.background = `linear-gradient(145deg, ${ball.color}, ${this.darkenColor(ball.color)})`;
        this.resultBall.textContent = ball.number;
        
        // 显示结果球（带动画）
        this.resultBall.classList.remove('hidden');
        this.resultBall.classList.add('falling');
        
        // 显示结果文本
        this.resultText.textContent = `恭喜！您抽中了 ${ball.number} 号球！`;
        
        // 移除动画类
        setTimeout(() => {
            this.resultBall.classList.remove('falling');
        }, 1000);
    }
    
    darkenColor(color) {
        // 简单的颜色加深函数
        const hex = color.replace('#', '');
        const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 30);
        const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 30);
        const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 30);
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }
}

// 初始化扭蛋机
document.addEventListener('DOMContentLoaded', () => {
    new TwistEggMachine();
});
