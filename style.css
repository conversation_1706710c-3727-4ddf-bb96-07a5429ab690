* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1 {
    color: white;
    margin-bottom: 30px;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.machine {
    background: linear-gradient(145deg, #ff6b6b, #ee5a52);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    margin-bottom: 30px;
}

.machine-top {
    background: linear-gradient(145deg, #4ecdc4, #44a08d);
    border-radius: 15px 15px 0 0;
    padding: 20px;
    margin-bottom: 10px;
}

.glass-dome {
    background: rgba(255, 255, 255, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 10px;
    backdrop-filter: blur(5px);
}

#ballCanvas {
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
}

.machine-middle {
    background: linear-gradient(145deg, #ffeaa7, #fdcb6e);
    padding: 15px;
    margin-bottom: 10px;
}

.dispenser {
    background: #2d3436;
    height: 40px;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.dispenser-opening {
    width: 30px;
    height: 30px;
    background: #000;
    border-radius: 50%;
    border: 2px solid #636e72;
}

.machine-bottom {
    background: linear-gradient(145deg, #a29bfe, #6c5ce7);
    border-radius: 0 0 15px 15px;
    padding: 20px;
}

.draw-button {
    background: linear-gradient(145deg, #fd79a8, #e84393);
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.2em;
    font-weight: bold;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.draw-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 20px rgba(0, 0, 0, 0.3);
}

.draw-button:active {
    transform: translateY(0);
}

.draw-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.result-area {
    margin-top: 20px;
}

.result-ball {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5em;
    font-weight: bold;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: all 0.5s ease;
}

.result-ball.hidden {
    opacity: 0;
    transform: scale(0);
}

.result-text {
    color: white;
    font-size: 1.3em;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shaking {
    animation: shake 0.1s infinite;
}

@keyframes fallDown {
    0% {
        transform: translateY(-50px);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.falling {
    animation: fallDown 1s ease-out;
}
